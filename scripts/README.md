# 自动化测试脚本

## 模板管理测试脚本

这个 Puppeteer 脚本用于自动化测试模板管理模块的新增和删除功能。

### 安装依赖

```bash
npm install puppeteer
```

### 运行测试

1. **启动开发服务器**
   ```bash
   npm run dev
   ```

2. **运行模板管理测试**
   ```bash
   npm run test:template
   ```

   或者直接运行：
   ```bash
   node scripts/test-template-management.js
   ```

### 测试流程

脚本将执行以下测试步骤：

1. **环境准备**
   - 启动浏览器（默认有界面模式，便于观察）
   - 配置页面监听器

2. **用户登录**
   - 访问登录页面
   - 输入管理员凭据（admin/123456）
   - 验证登录成功

3. **导航到模板管理**
   - 跳转到管理员页面
   - 确认模板管理界面加载

4. **测试新增模板**
   - 点击新增模板按钮
   - 填写模板信息：
     - 名称：自动化测试模板
     - 描述：这是一个由自动化测试创建的模板
     - 类型：okr
   - 提交表单
   - 验证成功 Toast 消息

5. **验证模板创建**
   - 确认新创建的模板在列表中显示

6. **测试删除模板**
   - 定位刚创建的模板
   - 点击删除按钮
   - 确认删除操作
   - 验证删除成功 Toast 消息

7. **验证模板删除**
   - 确认模板从列表中移除

### 配置选项

在脚本顶部的 `config` 对象中可以修改：

```javascript
const config = {
  baseUrl: 'http://localhost:3000',    // 应用地址
  credentials: {
    username: 'admin',                 // 测试用户名
    password: '123456'                 // 测试密码
  },
  testTemplate: {
    name: '自动化测试模板',            // 测试模板名称
    description: '测试描述',           // 测试模板描述
    type: 'okr'                       // 模板类型
  },
  timeout: 30000,                     // 超时时间（毫秒）
  slowMo: 100                         // 操作延迟（毫秒）
};
```

### 运行模式

#### 有界面模式（默认）
```javascript
headless: false  // 可以看到浏览器界面
```

#### 无界面模式（后台运行）
```javascript
headless: true   // 后台运行，更快但无法观察
```

### 测试报告

脚本会生成详细的测试报告，包括：
- 总测试数
- 通过/失败数量
- 成功率
- 每个测试步骤的详细结果

示例输出：
```
📊 测试报告
==================================================
总测试数: 8
通过: 8
失败: 0
成功率: 100.0%

详细结果:
1. ✅ 用户登录: 登录成功
2. ✅ 导航到模板管理: 成功进入模板管理页面
3. ✅ 新增弹窗显示: 新增模板弹窗已出现
4. ✅ 模板创建: 模板 "自动化测试模板" 创建成功
5. ✅ 查找创建的模板: 找到刚创建的模板
6. ✅ 删除确认弹窗: 删除确认弹窗已出现
7. ✅ 模板删除: 模板 "自动化测试模板" 删除成功
8. ✅ 验证模板删除: 模板已成功删除
==================================================
🎉 所有测试通过！
```

### 故障排除

#### 常见问题

1. **登录失败**
   - 检查用户名密码是否正确
   - 确认后端服务正常运行

2. **元素未找到**
   - 检查页面是否完全加载
   - 确认页面结构是否有变化

3. **超时错误**
   - 增加 timeout 配置值
   - 检查网络连接和服务响应速度

4. **Toast 消息未检测到**
   - 确认 Sonner Toast 组件正常工作
   - 检查 Toast 选择器是否正确

#### 调试模式

可以在脚本中添加以下代码来帮助调试：

```javascript
// 启用详细日志
this.page.on('console', msg => console.log('页面日志:', msg.text()));

// 保存页面截图
await this.page.screenshot({ path: 'debug.png', fullPage: true });

// 等待人工检查
await this.page.waitForTimeout(5000);
```

### 扩展测试

可以基于这个脚本扩展更多测试功能：

1. **模板编辑测试**
2. **权重验证测试**
3. **模板搜索和筛选测试**
4. **批量操作测试**
5. **错误处理测试**

### 注意事项

1. **数据清理**：脚本会创建和删除测试数据，正常情况下不会影响现有数据
2. **并发运行**：避免同时运行多个测试实例
3. **环境要求**：确保开发环境正常运行且数据库连接正常
4. **权限要求**：使用管理员账户进行测试